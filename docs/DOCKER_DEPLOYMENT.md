# 🐳 SoulVoice Docker 部署指南

## 📋 概述

本指南提供了 SoulVoice 语音合成平台的完整 Docker 部署方案，包括开发、测试和生产环境的配置。

## 🎯 部署架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Supabase Edge   │    │   PostgreSQL    │
│   (Nginx)       │◄──►│   Functions      │◄──►│   (Supabase)    │
│   Port: 80      │    │   (Cloud)        │    │   (Cloud)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 检查 Docker 版本
docker --version
docker-compose --version

# 克隆项目
git clone <repository-url>
cd soulvoice
```

### 2. 配置环境变量

```bash
# 复制环境配置文件
cp .env.docker.example .env.docker

# 编辑配置文件
vim .env.docker
```

### 3. 一键部署

```bash
# 快速部署到生产环境
chmod +x scripts/quick-deploy.sh
./scripts/quick-deploy.sh
```

## 📦 部署选项

### 开发环境

```bash
# 启动开发环境
chmod +x scripts/dev-docker.sh
./scripts/dev-docker.sh

# 访问地址: http://localhost:8080
```

### 测试环境

```bash
# 部署测试环境
./scripts/docker-deploy.sh test --health-check
```

### 生产环境

```bash
# 生产环境部署
./scripts/docker-deploy.sh prod -v 1.0.0 --clean --health-check

# 使用 Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 高级配置

### 自定义构建

```bash
# 仅构建镜像
./scripts/docker-deploy.sh --build-only --no-cache

# 指定版本构建
./scripts/docker-deploy.sh prod -v 2.0.0 --build-only
```

### 资源监控

```bash
# 启动监控服务
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# 访问监控面板
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000
```

### 负载均衡

```bash
# 启动负载均衡器
docker-compose -f docker-compose.prod.yml --profile loadbalancer up -d
```

## 📊 性能优化

### 镜像优化
- **多阶段构建**: 减少镜像体积至 ~50MB
- **Alpine Linux**: 使用轻量级基础镜像
- **静态资源**: Nginx 高效服务静态文件

### 运行时优化
- **Gzip 压缩**: 减少传输体积
- **缓存策略**: 优化资源加载速度
- **健康检查**: 自动故障恢复

## 🛡️ 安全配置

### 容器安全
```bash
# 非 root 用户运行
USER nginx-user

# 只读文件系统
read_only: true

# 安全选项
security_opt:
  - no-new-privileges:true
```

### 网络安全
```bash
# 自定义网络
networks:
  soulvoice-network:
    driver: bridge
```

## 📈 监控和日志

### 日志配置
```yaml
logging:
  driver: "json-file"
  options:
    max-size: "50m"
    max-file: "5"
    compress: "true"
```

### 健康检查
```bash
# 检查服务状态
curl http://localhost/health

# 查看容器状态
docker ps
docker logs soulvoice-frontend
```

## 🔄 维护操作

### 更新部署
```bash
# 滚动更新
./scripts/docker-deploy.sh prod -v 1.1.0

# 零停机更新
docker-compose -f docker-compose.prod.yml up -d --no-deps soulvoice-frontend
```

### 备份和恢复
```bash
# 备份数据
docker run --rm -v soulvoice_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data

# 恢复数据
docker run --rm -v soulvoice_data:/data -v $(pwd):/backup alpine tar xzf /backup/backup.tar.gz -C /
```

### 清理资源
```bash
# 清理未使用的资源
docker system prune -a

# 清理特定项目资源
./scripts/docker-deploy.sh --clean
```

## 🚨 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker logs soulvoice-frontend
   
   # 检查配置
   docker-compose config
   ```

2. **端口冲突**
   ```bash
   # 修改端口映射
   ./scripts/docker-deploy.sh dev -p 8080
   ```

3. **内存不足**
   ```bash
   # 调整资源限制
   # 编辑 docker-compose.yml 中的 resources 配置
   ```

### 性能调优

1. **增加并发处理能力**
   ```nginx
   worker_processes auto;
   worker_connections 2048;
   ```

2. **优化缓存策略**
   ```nginx
   location ~* \.(js|css)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 📱 微信: SoulVoice-Support
- 🌐 文档: https://docs.soulvoice.com
- 🐛 问题反馈: https://github.com/your-org/soulvoice/issues

---

**注意**: 生产环境部署前请确保已正确配置所有环境变量和安全设置。
