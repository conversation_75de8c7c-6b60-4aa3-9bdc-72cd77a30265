# =============================================================================
# SoulVoice Docker 环境配置示例
# 复制此文件为 .env.docker 并填入实际值
# =============================================================================

# -----------------------------------------------------------------------------
# 应用基础配置
# -----------------------------------------------------------------------------
# 应用名称
APP_NAME=SoulVoice

# 环境类型 (development/test/production)
NODE_ENV=production

# 应用版本
VERSION=1.0.0

# 服务端口
PORT=80
HTTPS_PORT=443

# 域名配置
DOMAIN=your-domain.com
SUBDOMAIN=api.your-domain.com

# -----------------------------------------------------------------------------
# Supabase 配置
# -----------------------------------------------------------------------------
# Supabase 项目 URL
VITE_SUPABASE_URL=https://your-project.supabase.co

# Supabase 匿名密钥
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase 服务密钥（仅服务端使用）
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# -----------------------------------------------------------------------------
# API 配置
# -----------------------------------------------------------------------------
# API 基础 URL
VITE_API_BASE_URL=https://your-project.supabase.co/functions/v1

# SiliconFlow API 配置
SILICONFLOW_API_KEY=your_siliconflow_api_key
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# -----------------------------------------------------------------------------
# 支付配置
# -----------------------------------------------------------------------------
# 微信支付配置
WECHAT_PAY_APP_ID=your_wechat_app_id
WECHAT_PAY_MCH_ID=your_wechat_mch_id
WECHAT_PAY_API_KEY=your_wechat_api_key
WECHAT_PAY_CERT_PATH=/path/to/cert.pem
WECHAT_PAY_KEY_PATH=/path/to/key.pem

# -----------------------------------------------------------------------------
# 数据库配置
# -----------------------------------------------------------------------------
# PostgreSQL 连接字符串（如果使用自托管数据库）
DATABASE_URL=postgresql://user:password@localhost:5432/soulvoice

# Redis 配置（如果使用缓存）
REDIS_URL=redis://localhost:6379

# -----------------------------------------------------------------------------
# 安全配置
# -----------------------------------------------------------------------------
# JWT 密钥
JWT_SECRET=your_jwt_secret_key

# 加密密钥
ENCRYPTION_KEY=your_encryption_key

# CORS 允许的域名
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# -----------------------------------------------------------------------------
# 监控和日志配置
# -----------------------------------------------------------------------------
# 日志级别 (debug/info/warn/error)
LOG_LEVEL=info

# Sentry DSN（错误监控）
SENTRY_DSN=your_sentry_dsn

# 性能监控
ENABLE_METRICS=true
METRICS_PORT=9090

# -----------------------------------------------------------------------------
# Docker 特定配置
# -----------------------------------------------------------------------------
# Docker 镜像标签
DOCKER_IMAGE_TAG=latest

# Docker 注册表
DOCKER_REGISTRY=your-registry.com

# 容器资源限制
MEMORY_LIMIT=512m
CPU_LIMIT=0.5

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# -----------------------------------------------------------------------------
# SSL/TLS 配置
# -----------------------------------------------------------------------------
# SSL 证书路径
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# Let's Encrypt 配置
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_STAGING=false

# -----------------------------------------------------------------------------
# 备份配置
# -----------------------------------------------------------------------------
# 备份存储路径
BACKUP_PATH=/backups

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# S3 备份配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=your-backup-bucket
AWS_REGION=us-west-2

# -----------------------------------------------------------------------------
# 开发配置
# -----------------------------------------------------------------------------
# 开发模式
DEBUG=false

# 热重载
HOT_RELOAD=false

# API 调试
API_DEBUG=false

# 性能分析
ENABLE_PROFILING=false
