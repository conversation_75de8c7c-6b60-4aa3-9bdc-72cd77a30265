# =============================================================================
# SoulVoice Docker Compose 配置
# 生产环境容器编排配置
# =============================================================================

version: '3.8'

services:
  # ---------------------------------------------------------------------------
  # SoulVoice 前端服务
  # ---------------------------------------------------------------------------
  soulvoice-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: soulvoice:latest
    container_name: soulvoice-frontend
    
    # 端口映射
    ports:
      - "80:80"
      - "443:443"  # 如果需要HTTPS
    
    # 环境变量
    environment:
      - NODE_ENV=production
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 重启策略
    restart: unless-stopped
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # 网络配置
    networks:
      - soulvoice-network
    
    # 卷挂载（如果需要持久化日志）
    volumes:
      - nginx-logs:/var/log/nginx
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 标签
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.soulvoice.rule=Host(`your-domain.com`)"
      - "traefik.http.routers.soulvoice.tls=true"
      - "traefik.http.routers.soulvoice.tls.certresolver=letsencrypt"

# -----------------------------------------------------------------------------
# 网络配置
# -----------------------------------------------------------------------------
networks:
  soulvoice-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# -----------------------------------------------------------------------------
# 卷配置
# -----------------------------------------------------------------------------
volumes:
  nginx-logs:
    driver: local
