# =============================================================================
# SoulVoice Docker 忽略文件配置
# 优化构建性能，减少镜像体积
# =============================================================================

# -----------------------------------------------------------------------------
# Node.js 相关
# -----------------------------------------------------------------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn
.pnpm-debug.log*

# -----------------------------------------------------------------------------
# 构建产物和缓存
# -----------------------------------------------------------------------------
dist/
build/
.cache/
.parcel-cache/
.vite/
.turbo/

# -----------------------------------------------------------------------------
# 开发工具和配置
# -----------------------------------------------------------------------------
.vscode/
.idea/
*.swp
*.swo
*~

# -----------------------------------------------------------------------------
# 版本控制
# -----------------------------------------------------------------------------
.git/
.gitignore
.gitattributes
.github/

# -----------------------------------------------------------------------------
# 环境配置文件
# -----------------------------------------------------------------------------
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# -----------------------------------------------------------------------------
# 日志文件
# -----------------------------------------------------------------------------
logs/
*.log
lerna-debug.log*

# -----------------------------------------------------------------------------
# 测试相关
# -----------------------------------------------------------------------------
coverage/
.nyc_output/
.jest/
test-results/
playwright-report/

# -----------------------------------------------------------------------------
# 文档和说明
# -----------------------------------------------------------------------------
README.md
CHANGELOG.md
LICENSE
docs/
*.md

# -----------------------------------------------------------------------------
# Docker 相关
# -----------------------------------------------------------------------------
Dockerfile*
docker-compose*.yml
.dockerignore

# -----------------------------------------------------------------------------
# 部署脚本
# -----------------------------------------------------------------------------
scripts/deploy*.sh
scripts/build*.sh
.deployment/

# -----------------------------------------------------------------------------
# 临时文件
# -----------------------------------------------------------------------------
.tmp/
temp/
*.tmp
*.temp

# -----------------------------------------------------------------------------
# 操作系统文件
# -----------------------------------------------------------------------------
.DS_Store
Thumbs.db
desktop.ini

# -----------------------------------------------------------------------------
# 编辑器配置
# -----------------------------------------------------------------------------
.editorconfig
.eslintrc*
.prettierrc*
tsconfig*.json
vite.config.ts
postcss.config.js
tailwind.config.js

# -----------------------------------------------------------------------------
# 包管理器锁文件（保留package-lock.json用于精确依赖）
# -----------------------------------------------------------------------------
yarn.lock
pnpm-lock.yaml

# -----------------------------------------------------------------------------
# Supabase 本地开发文件
# -----------------------------------------------------------------------------
supabase/.branches/
supabase/.temp/
supabase/seed.sql
