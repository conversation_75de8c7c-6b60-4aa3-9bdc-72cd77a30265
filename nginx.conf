# =============================================================================
# SoulVoice Nginx 高性能配置
# 优化静态文件服务，支持SPA路由和缓存策略
# =============================================================================

# -----------------------------------------------------------------------------
# 全局配置
# -----------------------------------------------------------------------------
user nginx-user;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# -----------------------------------------------------------------------------
# 事件配置
# -----------------------------------------------------------------------------
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# -----------------------------------------------------------------------------
# HTTP 配置
# -----------------------------------------------------------------------------
http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;
    
    # 隐藏Nginx版本信息
    server_tokens off;
    
    # -----------------------------------------------------------------------------
    # Gzip 压缩配置
    # -----------------------------------------------------------------------------
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # -----------------------------------------------------------------------------
    # 服务器配置
    # -----------------------------------------------------------------------------
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;
        
        # 安全头配置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        # -----------------------------------------------------------------------------
        # 静态资源缓存策略
        # -----------------------------------------------------------------------------
        
        # HTML文件 - 不缓存，确保更新及时
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
        
        # CSS和JS文件 - 长期缓存
        location ~* \.(css|js)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
        
        # 图片和字体文件 - 长期缓存
        location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
        
        # JSON文件 - 中期缓存
        location ~* \.json$ {
            expires 1d;
            add_header Cache-Control "public";
        }
        
        # -----------------------------------------------------------------------------
        # 健康检查端点
        # -----------------------------------------------------------------------------
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # -----------------------------------------------------------------------------
        # API代理配置（如果需要）
        # -----------------------------------------------------------------------------
        location /api/ {
            # 如果有本地API服务，可以在这里配置代理
            # proxy_pass http://backend:3000;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;
            
            # 当前配置：返回404（因为API通过Supabase Edge Functions提供）
            return 404;
        }
        
        # -----------------------------------------------------------------------------
        # SPA路由支持
        # -----------------------------------------------------------------------------
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # -----------------------------------------------------------------------------
        # 错误页面配置
        # -----------------------------------------------------------------------------
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
