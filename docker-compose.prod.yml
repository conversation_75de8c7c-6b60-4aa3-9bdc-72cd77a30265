# =============================================================================
# SoulVoice 生产环境 Docker Compose 配置
# 高可用、高性能的生产环境部署配置
# =============================================================================

version: '3.8'

services:
  # ---------------------------------------------------------------------------
  # SoulVoice 前端服务 - 生产环境配置
  # ---------------------------------------------------------------------------
  soulvoice-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
      args:
        - NODE_ENV=production
    image: soulvoice:${VERSION:-latest}
    container_name: soulvoice-frontend-prod
    
    # 端口映射
    ports:
      - "80:80"
      - "443:443"
    
    # 环境变量
    environment:
      - NODE_ENV=production
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=2048
    
    # 生产环境资源配置
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
    
    # 增强的健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 30s
    
    # 重启策略
    restart: unless-stopped
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        compress: "true"
    
    # 网络配置
    networks:
      - soulvoice-prod-network
      - monitoring-network
    
    # 卷挂载
    volumes:
      - nginx-logs:/var/log/nginx
      - nginx-cache:/var/cache/nginx
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/run
    
    # 标签
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.soulvoice-prod.rule=Host(`${DOMAIN:-soulvoice.com}`)"
      - "traefik.http.routers.soulvoice-prod.tls=true"
      - "traefik.http.routers.soulvoice-prod.tls.certresolver=letsencrypt"
      - "traefik.http.services.soulvoice-prod.loadbalancer.server.port=80"
      - "traefik.http.middlewares.soulvoice-compress.compress=true"
      - "traefik.http.routers.soulvoice-prod.middlewares=soulvoice-compress"

  # ---------------------------------------------------------------------------
  # Nginx 负载均衡器（可选）
  # ---------------------------------------------------------------------------
  nginx-lb:
    image: nginx:alpine
    container_name: soulvoice-nginx-lb
    ports:
      - "8080:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - soulvoice-frontend
    networks:
      - soulvoice-prod-network
    restart: unless-stopped
    profiles:
      - loadbalancer

  # ---------------------------------------------------------------------------
  # 监控服务（可选）
  # ---------------------------------------------------------------------------
  prometheus:
    image: prom/prometheus:latest
    container_name: soulvoice-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - monitoring-network
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: soulvoice-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - monitoring-network
    restart: unless-stopped
    profiles:
      - monitoring

# -----------------------------------------------------------------------------
# 网络配置
# -----------------------------------------------------------------------------
networks:
  soulvoice-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# -----------------------------------------------------------------------------
# 卷配置
# -----------------------------------------------------------------------------
volumes:
  nginx-logs:
    driver: local
  nginx-cache:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
