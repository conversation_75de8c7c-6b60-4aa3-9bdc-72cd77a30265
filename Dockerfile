# =============================================================================
# SoulVoice Docker 多阶段构建配置
# 轻量化、高性能的生产环境部署方案
# =============================================================================

# -----------------------------------------------------------------------------
# 阶段1: 构建阶段 (Builder Stage)
# 使用 Node.js 18 Alpine 镜像进行前端资源构建
# -----------------------------------------------------------------------------
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（提升国内下载速度）
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set network-timeout 300000

# 复制package文件并安装依赖（包括开发依赖用于构建）
COPY package*.json ./
RUN npm install --production=false --silent

# 复制源代码
COPY . .

# 构建生产版本
RUN npm run build

# 清理构建缓存
RUN npm cache clean --force

# -----------------------------------------------------------------------------
# 阶段2: 运行阶段 (Runtime Stage)  
# 使用轻量级 Nginx Alpine 镜像提供静态文件服务
# -----------------------------------------------------------------------------
FROM nginx:1.25-alpine AS runtime

# 安装必要工具（用于健康检查）
RUN apk add --no-cache curl

# 创建非root用户
RUN addgroup -g 1001 -S nginx-user && \
    adduser -S nginx-user -G nginx-user

# 从构建阶段复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制自定义Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建健康检查端点
RUN echo '<!DOCTYPE html><html><head><title>Health Check</title></head><body><h1>OK</h1></body></html>' > /usr/share/nginx/html/health

# 设置正确的文件权限
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html && \
    chown -R nginx-user:nginx-user /var/cache/nginx && \
    chown -R nginx-user:nginx-user /var/log/nginx && \
    chown -R nginx-user:nginx-user /etc/nginx/conf.d

# 创建nginx运行时需要的目录
RUN touch /var/run/nginx.pid && \
    chown nginx-user:nginx-user /var/run/nginx.pid

# 暴露端口
EXPOSE 80

# 健康检查配置
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 切换到非root用户
USER nginx-user

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]

# -----------------------------------------------------------------------------
# 镜像元数据
# -----------------------------------------------------------------------------
LABEL maintainer="SoulVoice Team"
LABEL version="1.0.0"
LABEL description="SoulVoice 语音合成平台 - 轻量化生产环境镜像"
LABEL org.opencontainers.image.source="https://github.com/your-org/soulvoice"
