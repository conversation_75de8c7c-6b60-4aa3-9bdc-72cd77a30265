#!/bin/bash

# =============================================================================
# SoulVoice 开发环境 Docker 脚本
# 快速启动开发环境容器
# =============================================================================

set -euo pipefail

GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 启动 SoulVoice 开发环境...${NC}"

# 配置代理
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 构建开发镜像
echo "🔨 构建开发镜像..."
docker build -t soulvoice:dev .

# 启动开发容器
echo "🐳 启动开发容器..."
docker run -d \
  --name soulvoice-dev \
  -p 8080:80 \
  --restart unless-stopped \
  soulvoice:dev

echo -e "${GREEN}"
echo "✅ 开发环境启动成功！"
echo "🌐 访问地址: http://localhost:8080"
echo "🔍 查看日志: docker logs soulvoice-dev"
echo "🛑 停止容器: docker stop soulvoice-dev"
echo -e "${NC}"
