#!/bin/bash

# =============================================================================
# SoulVoice 快速部署脚本
# 一键部署到生产环境
# =============================================================================

set -euo pipefail

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}"
cat << "EOF"
   ____             ___    __      _          
  / __/__  __ __   / / |  / /__   (_)________ 
 _\ \ / _ \/ // /  / /| | / / _ \ / / __/ __/ 
/___/ \___/\_,_/  /_/ |_|/_/\___//_/\__/\__/  
                                              
🎵 语音合成平台 - 快速部署工具
EOF
echo -e "${NC}"

echo -e "${YELLOW}正在执行快速部署...${NC}"

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -f "Dockerfile" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

# 设置代理（如果需要）
echo "🌐 配置代理..."
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 构建并部署
echo "🔨 构建和部署..."
chmod +x scripts/docker-deploy.sh
./scripts/docker-deploy.sh prod --clean --health-check

echo -e "${GREEN}"
echo "🎉 快速部署完成！"
echo "📱 访问地址: http://localhost"
echo "📊 健康检查: http://localhost/health"
echo -e "${NC}"
