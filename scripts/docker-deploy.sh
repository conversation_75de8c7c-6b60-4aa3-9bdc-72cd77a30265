#!/bin/bash

# =============================================================================
# SoulVoice Docker 自动化部署脚本
# 支持开发、测试、生产环境的一键部署
# =============================================================================

set -euo pipefail

# -----------------------------------------------------------------------------
# 配置变量
# -----------------------------------------------------------------------------
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
IMAGE_NAME="soulvoice"
CONTAINER_NAME="soulvoice-frontend"
DEFAULT_ENV="development"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 工具函数
# -----------------------------------------------------------------------------
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
SoulVoice Docker 部署脚本

用法: $0 [选项] [环境]

环境:
    dev         开发环境部署
    test        测试环境部署  
    prod        生产环境部署

选项:
    -h, --help              显示帮助信息
    -v, --version VERSION   指定镜像版本 (默认: latest)
    -p, --port PORT         指定端口 (默认: 80)
    -c, --clean             清理旧容器和镜像
    -b, --build-only        仅构建镜像，不部署
    -d, --deploy-only       仅部署，不构建
    --no-cache              构建时不使用缓存
    --health-check          部署后进行健康检查

示例:
    $0 dev                  # 开发环境部署
    $0 prod -v 1.0.0        # 生产环境部署指定版本
    $0 test --clean         # 测试环境部署并清理旧资源
    $0 --build-only         # 仅构建镜像

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 清理旧资源
cleanup() {
    log_info "清理旧容器和镜像..."
    
    # 停止并删除容器
    if docker ps -a --format 'table {{.Names}}' | grep -q "$CONTAINER_NAME"; then
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        log_success "已清理旧容器"
    fi
    
    # 删除悬空镜像
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
        log_success "已清理悬空镜像"
    fi
}

# 构建镜像
build_image() {
    local version=$1
    local no_cache=$2
    
    log_info "构建 Docker 镜像..."
    
    cd "$PROJECT_ROOT"
    
    local build_args=""
    if [ "$no_cache" = true ]; then
        build_args="--no-cache"
    fi
    
    docker build $build_args -t "${IMAGE_NAME}:${version}" -t "${IMAGE_NAME}:latest" .
    
    log_success "镜像构建完成: ${IMAGE_NAME}:${version}"
}

# 部署服务
deploy_service() {
    local env=$1
    local version=$2
    local port=$3
    
    log_info "部署 $env 环境..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export VERSION="$version"
    export PORT="$port"
    export ENVIRONMENT="$env"
    
    # 选择对应的 compose 文件
    local compose_file="docker-compose.yml"
    if [ "$env" = "prod" ]; then
        compose_file="docker-compose.prod.yml"
    fi
    
    # 启动服务
    docker-compose -f "$compose_file" up -d
    
    log_success "$env 环境部署完成"
}

# 健康检查
health_check() {
    local port=$1
    local max_attempts=30
    local attempt=1
    
    log_info "进行健康检查..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:$port/health" &>/dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 显示部署信息
show_deployment_info() {
    local env=$1
    local version=$2
    local port=$3
    
    cat << EOF

${GREEN}=============================================================================
🎉 SoulVoice 部署成功！
=============================================================================${NC}

环境信息:
  • 环境: $env
  • 版本: $version  
  • 端口: $port
  • 访问地址: http://localhost:$port

容器信息:
$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep soulvoice)

有用的命令:
  • 查看日志: docker logs $CONTAINER_NAME
  • 进入容器: docker exec -it $CONTAINER_NAME sh
  • 停止服务: docker-compose down
  • 重启服务: docker-compose restart

${GREEN}==============================================================================${NC}

EOF
}

# -----------------------------------------------------------------------------
# 主函数
# -----------------------------------------------------------------------------
main() {
    local env="$DEFAULT_ENV"
    local version="latest"
    local port="80"
    local clean=false
    local build_only=false
    local deploy_only=false
    local no_cache=false
    local health_check_enabled=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                version="$2"
                shift 2
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -c|--clean)
                clean=true
                shift
                ;;
            -b|--build-only)
                build_only=true
                shift
                ;;
            -d|--deploy-only)
                deploy_only=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --health-check)
                health_check_enabled=true
                shift
                ;;
            dev|test|prod)
                env="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行部署流程
    log_info "开始 SoulVoice Docker 部署流程..."
    log_info "环境: $env, 版本: $version, 端口: $port"
    
    check_dependencies
    
    if [ "$clean" = true ]; then
        cleanup
    fi
    
    if [ "$deploy_only" = false ]; then
        build_image "$version" "$no_cache"
    fi
    
    if [ "$build_only" = false ]; then
        deploy_service "$env" "$version" "$port"
        
        if [ "$health_check_enabled" = true ]; then
            health_check "$port"
        fi
        
        show_deployment_info "$env" "$version" "$port"
    fi
    
    log_success "部署流程完成！"
}

# 执行主函数
main "$@"
